'use client';

import { useState, useEffect } from 'react';
import { FaTrash, FaSync, FaInfo, FaCog } from 'react-icons/fa';
import cacheManager from '../lib/cacheManager';

export default function DevCacheControl() {
  const [isVisible, setIsVisible] = useState(false);
  const [cacheStatus, setCacheStatus] = useState(null);
  const [isClearing, setIsClearing] = useState(false);

  // Only show in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setIsVisible(true);
      updateCacheStatus();
    }
  }, []);

  const updateCacheStatus = async () => {
    const status = await cacheManager.getCacheStatus();
    setCacheStatus(status);
  };

  const handleClearAll = async () => {
    setIsClearing(true);
    await cacheManager.clearAllCaches();
    await updateCacheStatus();
    setIsClearing(false);
    
    // Show success message
    alert('✅ All caches cleared! The page will reload to show fresh content.');
    window.location.reload();
  };

  const handleClearMemory = async () => {
    cacheManager.clearMemoryCache();
    await updateCacheStatus();
  };

  const handleClearServiceWorker = async () => {
    await cacheManager.clearServiceWorkerCache();
    await updateCacheStatus();
  };

  const handleForceReload = () => {
    cacheManager.forceReload();
  };

  const handleClearArticles = async () => {
    const cleared = cacheManager.clearArticleCache();
    await updateCacheStatus();
    alert(`✅ Cleared ${cleared} article cache entries!`);
  };

  const handleClearByPattern = async () => {
    const pattern = prompt('Enter cache pattern to clear (e.g., "article", "categories"):');
    if (pattern) {
      const cleared = cacheManager.clearByPattern(pattern);
      await updateCacheStatus();
      alert(`✅ Cleared ${cleared} cache entries matching "${pattern}"!`);
    }
  };

  const handleClearAllContent = async () => {
    const cleared = cacheManager.clearAllContentCache();
    await updateCacheStatus();
    alert(`✅ Cleared ${cleared} total content cache entries!`);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-red-600 hover:bg-red-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Development Cache Control"
      >
        <FaCog className="text-lg" />
      </button>

      {/* Cache Control Panel */}
      {isVisible && (
        <div className="absolute bottom-16 right-0 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-bold text-gray-800 flex items-center gap-2">
              <FaCog /> Cache Control
            </h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>

          {/* Cache Status */}
          {cacheStatus && (
            <div className="mb-4 p-3 bg-gray-50 rounded">
              <h4 className="font-semibold text-sm text-gray-700 mb-2 flex items-center gap-1">
                <FaInfo /> Cache Status
              </h4>
              <div className="text-xs text-gray-600 space-y-1">
                <div>Memory Cache: {cacheStatus.memoryCache.size} items</div>
                <div>SW Caches: {cacheStatus.serviceWorkerCache.cacheNames.length}</div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            <button
              onClick={handleClearAll}
              disabled={isClearing}
              className="w-full bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaTrash />
              {isClearing ? 'Clearing...' : 'Clear All Caches'}
            </button>

            <button
              onClick={handleClearMemory}
              className="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaTrash />
              Clear Memory Cache
            </button>

            <button
              onClick={handleClearServiceWorker}
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaTrash />
              Clear SW Cache
            </button>

            <button
              onClick={handleForceReload}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaSync />
              Force Reload
            </button>

            <button
              onClick={handleClearArticles}
              className="w-full bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaTrash />
              Clear Article Cache
            </button>

            <button
              onClick={handleClearAllContent}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaTrash />
              Clear All Content
            </button>

            <button
              onClick={handleClearByPattern}
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaTrash />
              Clear by Pattern
            </button>

            <button
              onClick={updateCacheStatus}
              className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center justify-center gap-2 text-sm"
            >
              <FaSync />
              Refresh Status
            </button>
          </div>

          {/* Instructions */}
          <div className="mt-4 p-3 bg-blue-50 rounded text-xs text-blue-800">
            <strong>Quick Fix:</strong> Click "Clear All Caches" to see your latest changes immediately.
          </div>

          {/* Console Commands */}
          <div className="mt-3 p-3 bg-gray-50 rounded text-xs text-gray-600">
            <strong>Console Commands:</strong>
            <div className="mt-1 font-mono space-y-1 text-xs">
              <div>clearCache()</div>
              <div>clearAllContentCache()</div>
              <div>clearArticleCache()</div>
              <div>clearTestimonyCache()</div>
              <div>clearCategoryCache()</div>
              <div>clearByPattern('pattern')</div>
              <div>forceReload()</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
