// Simple in-memory cache with TTL (Time To Live)
class Cache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  set(key, value, ttl = 300000) { // Default 5 minutes TTL
    // In development, use shorter TTL to see changes faster
    if (process.env.NODE_ENV === 'development') {
      ttl = Math.min(ttl, 15000); // Max 15 seconds in development for faster updates
    } else {
      // In production, reduce TTL for articles to see updates faster
      if (key.includes('article') || key.includes('articles')) {
        ttl = Math.min(ttl, 120000); // Max 2 minutes for articles in production
      }
    }
    // Clear existing timer if any
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // Set the value
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });

    // Set expiration timer
    const timer = setTimeout(() => {
      this.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  delete(key) {
    // Clear timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }

    // Remove from cache
    this.cache.delete(key);
  }

  clear() {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    this.cache.clear();
  }

  has(key) {
    const item = this.cache.get(key);
    if (!item) return false;
    
    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  size() {
    return this.cache.size;
  }

  // Clear cache entries by pattern
  clearByPattern(pattern) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  // Clear all article-related cache
  clearArticleCache() {
    const cleared = this.clearByPattern('article');
    console.log(`✅ Cleared ${cleared} article cache entries`);
    return cleared;
  }

  // Clear all cache entries for a specific article slug
  clearArticleBySlug(slug) {
    const patterns = [`article:${slug}`, `articles:`];
    let totalCleared = 0;
    patterns.forEach(pattern => {
      totalCleared += this.clearByPattern(pattern);
    });
    console.log(`✅ Cleared ${totalCleared} cache entries for article: ${slug}`);
    return totalCleared;
  }
}

// Create a global cache instance
const cache = new Cache();

// Cache key generators
export const cacheKeys = {
  articles: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `articles:${key}`;
  },
  
  article: (slug) => `article:${slug}`,
  
  categories: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `categories:${key}`;
  },
  
  category: (slug) => `category:${slug}`,

  testimonies: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `testimonies:${key}`;
  },
  
  testimony: (id) => `testimony:${id}`,
  
  partners: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `partners:${key}`;
  },
  
  partner: (slug) => `partner:${slug}`,
  
  authors: (params = {}) => {
    const key = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `authors:${key}`;
  },
  
  author: (id) => `author:${id}`,
  
  homepage: () => 'homepage:data'
};

export default cache;
