// Social sharing utilities
import { stripHtml } from './htmlUtils';

/**
 * Generate social media sharing URLs
 */
export const socialShareUrls = {
  facebook: (url, title, description = '') => {
    // Clean title and description using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanDescription = stripHtml(description);

    // Format quote with proper separation
    let quote = cleanTitle;
    if (cleanDescription && cleanDescription !== cleanTitle) {
      quote = `${cleanTitle} - ${cleanDescription}`;
    }

    const params = new URLSearchParams({
      u: url,
      quote: quote,
    });
    return `https://www.facebook.com/sharer/sharer.php?${params.toString()}`;
  },

  twitter: (url, title, hashtags = [], description = '') => {
    // Clean title and description using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanDescription = stripHtml(description);

    // Format text with proper separation
    let text = cleanTitle;
    if (cleanDescription && cleanDescription !== cleanTitle) {
      text = `${cleanTitle} - ${cleanDescription}`;
    }

    const params = new URLSearchParams({
      url: url,
      text: text,
      hashtags: hashtags.join(','),
    });
    return `https://twitter.com/intent/tweet?${params.toString()}`;
  },

  linkedin: (url, title, summary = '') => {
    // Clean title and summary using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanSummary = stripHtml(summary);

    const params = new URLSearchParams({
      url: url,
      title: cleanTitle,
      summary: cleanSummary,
    });
    return `https://www.linkedin.com/sharing/share-offsite/?${params.toString()}`;
  },

  whatsapp: (url, title, description = '') => {
    // Clean title and description using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanDescription = stripHtml(description);

    // Format text with proper separation
    let text = cleanTitle;
    if (cleanDescription && cleanDescription !== cleanTitle) {
      text = `${cleanTitle} - ${cleanDescription}`;
    }
    text = `${text}\n\n${url}`;

    const params = new URLSearchParams({
      text: text,
    });
    return `https://wa.me/?${params.toString()}`;
  },

  telegram: (url, title, description = '') => {
    // Clean title and description using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanDescription = stripHtml(description);

    // Format text with proper separation
    let text = cleanTitle;
    if (cleanDescription && cleanDescription !== cleanTitle) {
      text = `${cleanTitle} - ${cleanDescription}`;
    }

    const params = new URLSearchParams({
      url: url,
      text: text,
    });
    return `https://t.me/share/url?${params.toString()}`;
  },

  email: (url, title, body = '') => {
    // Clean title and body using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanBody = stripHtml(body);

    const subject = `Check out: ${cleanTitle}`;
    const emailBody = cleanBody || `I thought you might be interested in this: ${cleanTitle}\n\n${url}`;
    const params = new URLSearchParams({
      subject: subject,
      body: emailBody,
    });
    return `mailto:?${params.toString()}`;
  },

  // Note: Instagram doesn't support direct URL sharing via web links
  // This will copy the content to clipboard for manual sharing
  instagram: (url, title, description = '') => {
    // Clean title and description using stripHtml for consistency
    const cleanTitle = stripHtml(title);
    const cleanDescription = stripHtml(description);

    // Format content with proper separation
    let content = `**${cleanTitle}**`;
    if (cleanDescription && cleanDescription !== cleanTitle) {
      content = `**${cleanTitle}** - ${cleanDescription}`;
    }
    content = `${content}\n\n${url}\n\n#UmugoreUzashimwa #BiblicalPrinciples #WomenInspiration`;

    return {
      type: 'copy',
      content: content,
    };
  },
};

/**
 * Open social sharing popup window
 */
export const openShareWindow = (url, title = 'Share', width = 600, height = 400) => {
  if (typeof window === 'undefined') return;

  const left = (window.screen.width - width) / 2;
  const top = (window.screen.height - height) / 2;

  const popup = window.open(
    url,
    'share',
    `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
  );

  // Focus the popup window
  if (popup) {
    popup.focus();
  }

  return popup;
};

/**
 * Copy URL to clipboard
 */
export const copyToClipboard = async (text) => {
  if (typeof window === 'undefined') return false;

  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      return success;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Get default hashtags for different content types
 */
export const getDefaultHashtags = (type = 'article') => {
  const commonTags = ['UmugoreUzashimwa', 'BiblicalPrinciples', 'WomenInspiration'];
  
  switch (type) {
    case 'article':
      return [...commonTags, 'ChristianWomen', 'Faith'];
    case 'testimony':
      return [...commonTags, 'Testimony', 'FaithJourney'];
    default:
      return commonTags;
  }
};

/**
 * Generate sharing data for a specific content item
 */
export const generateShareData = (item, type = 'article', baseUrl = '') => {

  const url = type === 'article'
    ? `${baseUrl}/articles/${item.slug}`
    : `${baseUrl}/testimonies/${item.id}`;

  const title = type === 'article'
    ? item.title
    : `${item.name}'s Testimony`;

  // Get description with proper HTML entity handling using stripHtml
  let description = '';
  if (type === 'article') {
    if (item.short_description) {
      description = stripHtml(item.short_description);
      // If short_description is too long, truncate it
      if (description.length > 160) {
        description = description.substring(0, 160) + '...';
      }
    } else if (item.content) {
      description = stripHtml(item.content, 160);
    }
  } else {
    if (item.content) {
      description = stripHtml(item.content, 160);
    }
  }

  const hashtags = getDefaultHashtags(type);

  return {
    url,
    title,
    description,
    hashtags,
    image: item.image || null,
  };
};
