/**
 * Utility functions for handling HTML content from CKEditor
 */

/**
 * Strip HTML tags and decode HTML entities from text
 * @param {string} html - HTML string to clean
 * @param {number} maxLength - Maximum length of returned text (optional)
 * @returns {string} - Clean text without HTML tags
 */
export function stripHtml(html, maxLength = null) {
  if (!html) return '';
  
  // Remove HTML tags
  let text = html.replace(/<[^>]*>/g, '');
  
  // Decode common HTML entities (comprehensive list)
  text = text
    .replace(/&nbsp;/g, ' ')     // Non-breaking space
    .replace(/&amp;/g, '&')     // Ampersand
    .replace(/&lt;/g, '<')      // Less than
    .replace(/&gt;/g, '>')      // Greater than
    .replace(/&quot;/g, '"')    // Double quote
    .replace(/&#39;/g, "'")     // Single quote (numeric)
    .replace(/&apos;/g, "'")    // Single quote (named)
    .replace(/&hellip;/g, '...') // Ellipsis
    .replace(/&mdash;/g, '—')   // Em dash
    .replace(/&ndash;/g, '–')   // En dash
    .replace(/&lsquo;/g, "'")   // Left single quote
    .replace(/&rsquo;/g, "'")   // Right single quote
    .replace(/&ldquo;/g, '"')   // Left double quote
    .replace(/&rdquo;/g, '"')   // Right double quote
    .replace(/&copy;/g, '©')    // Copyright
    .replace(/&reg;/g, '®')     // Registered trademark
    .replace(/&trade;/g, '™');  // Trademark

  // Clean up extra whitespace
  text = text.replace(/\s+/g, ' ').trim();
  
  // Truncate if maxLength is specified
  if (maxLength && text.length > maxLength) {
    text = text.substring(0, maxLength) + '...';
  }
  
  return text;
}

/**
 * Sanitize HTML content for safe rendering
 * This is a basic sanitizer - for production, consider using a library like DOMPurify
 * @param {string} html - HTML string to sanitize
 * @returns {string} - Sanitized HTML
 */
export function sanitizeHtml(html) {
  if (!html) return '';
  
  // Remove potentially dangerous tags and attributes
  let sanitized = html
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
    .replace(/<object[^>]*>.*?<\/object>/gi, '')
    .replace(/<embed[^>]*>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '') // Remove event handlers
    .replace(/javascript:/gi, ''); // Remove javascript: URLs
  
  return sanitized;
}

/**
 * Extract a clean excerpt from HTML content
 * @param {string} html - HTML content
 * @param {number} length - Desired excerpt length
 * @returns {string} - Clean excerpt
 */
export function extractExcerpt(html, length = 150) {
  return stripHtml(html, length);
}

/**
 * Check if content contains HTML tags
 * @param {string} content - Content to check
 * @returns {boolean} - True if content contains HTML tags
 */
export function containsHtml(content) {
  if (!content) return false;
  return /<[^>]*>/g.test(content);
}
