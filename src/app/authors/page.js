'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import AuthorCard from '../../components/AuthorCard';
import Pagination from '../../components/Pagination';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorMessage from '../../components/ErrorMessage';
import { AuthorGridSkeleton } from '../../components/SkeletonCard';
import { api } from '../../lib/api';

export default function AuthorsPage() {
  const [authors, setAuthors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch authors
  const fetchAuthors = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        page: currentPage,
        pageSize: 12,
      };

      const response = await api.authors.getAll(params);
      setAuthors(response.results || []);
      setTotalPages(response.total_pages || 1);
    } catch (err) {
      setError(err.message);
      setAuthors([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuthors();
  }, [currentPage]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="bg-primary py-12 text-white">
        <div className="container-custom">
          <h1 className="mb-4">Abanditsi bacu</h1>
          <p className="text-lg max-w-3xl">
            Menya abagore n'abagabo bashishikajwe no gusangiza ubwenge bwabo, ubunyangamugayo, n'ubushishozi bwa Bibiliya binyuze kuri urubuga, mururimi rw'ikinyarwanda.
          </p>
        </div>
      </section>

      {/* Authors Grid */}
      <section className="section bg-neutral-100">
        <div className="container-custom">
          {loading ? (
            <AuthorGridSkeleton count={12} />
          ) : error ? (
            <ErrorMessage
              message={error}
              onRetry={fetchAuthors}
              className="max-w-md mx-auto"
            />
          ) : authors.length > 0 ? (
            <>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {authors.map((author) => (
                  <AuthorCard key={author.id} author={author} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                  className="mt-12"
                />
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-neutral-600 mb-2">Nta banditsi baraboneka</h3>
              <p className="text-neutral-500">Uze kugenzura hanyuma ko hagize abanditsi bashya.</p>
            </div>
          )}
        </div>
      </section>

      {/* Become an Author */}
      <section className="section bg-secondary-light">
        <div className="container-custom max-w-3xl mx-auto text-center">
          <h2 className="mb-6">Ba umwanditsi</h2>
          <p className="text-lg mb-8">
            Ese ufite ubwenge n'ubushishozi ushaka gusangira n'abagore bashaka ubuyobozi bwa Bibiliya? Twakira abanditsi b'abashyitsi bafite intego imwe na twe.
          </p>
          <div className="bg-white p-8 rounded-lg border border-neutral-200 text-left">
            <div className="mb-6">
              <h3 className="text-xl mb-4">Icyo dushaka</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Inyandiko zafasha abagore kumenya Umuremyi wabo, abo baribo ndetse nicyo bahamagariwe</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Inyandiko ziri mu cyongereza cyangwa mu kinyarwanda. Zihuje n'ukuri kw'ibyanditswe byera</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Ubuhamya bw'umuntu ku giti cye, buvuga ku kwizera kwe cyangwa ubundi buhamya bwafasha benshi</span>
                </li>
              </ul>
            </div>
            <div className="text-center">
              <a href="/contact" className="btn btn-primary">Ohereza igitekerezo cyawe cyo kwandika</a>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
