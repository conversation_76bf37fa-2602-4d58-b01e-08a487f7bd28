'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import PartnerCard from '../../components/PartnerCard';
import CategoryFilter from '../../components/CategoryFilter';
import Pagination from '../../components/Pagination';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorMessage from '../../components/ErrorMessage';
import { PartnerGridSkeleton } from '../../components/SkeletonCard';
import { api } from '../../lib/api';
import Link from 'next/link';

export default function PartnersPage() {
  // State management
  const [partners, setPartners] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCategories, setSelectedCategories] = useState([]);

  // Fetch partners
  const fetchPartners = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        page: currentPage,
        pageSize: 9,
      };

      if (selectedCategories.length > 0) {
        params.categories = selectedCategories.join(',');
      }

      const response = await api.partners.getAll(params);
      setPartners(response.results || []);
      setTotalPages(response.total_pages || 1);
    } catch (err) {
      setError(err.message);
      setPartners([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await api.categories.getAll({ pageSize: 100 });
      setCategories(response.results || []);
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchPartners();
  }, [currentPage, selectedCategories]);

  const handleCategoryChange = (categoryList) => {
    setSelectedCategories(categoryList);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="py-12 text-white bg-primary-light">
        <div className="container-custom">
          <h1 className="mb-4">Abafatanyabikorwa</h1>
          <p className="text-lg max-w-3xl">
            Turashimira Imana kubw'ibigo, n'abaterankunga badufasha kugera ku ntego ya minisiteri.
          </p>
        </div>
      </section>

      {/* Filter */}
      {/* <section className="py-8 bg-secondary border-b border-secondary-dark">
        <div className="container-custom">
          <CategoryFilter
            categories={categories}
            selectedCategories={selectedCategories}
            onCategoryChange={handleCategoryChange}
          /> */}

          {/* Active filters display */}
          {/* {selectedCategories.length > 0 && (
            <div className="mt-4 flex flex-wrap gap-2">
              {selectedCategories.map((categorySlug) => {
                const category = categories.find(c => c.slug === categorySlug);
                return category ? (
                  <span key={categorySlug} className="inline-flex items-center gap-1 px-3 py-1 bg-accent text-white text-sm rounded-full">
                    {category.name}
                    <button
                      onClick={() => handleCategoryChange(selectedCategories.filter(c => c !== categorySlug))}
                      className="ml-1 hover:text-red-200"
                    >
                      ×
                    </button>
                  </span>
                ) : null;
              })}
            </div>
          )}
        </div>
      </section> */}

      {/* Partners Grid */}
      <section className="section bg-background">
        <div className="container-custom">
          {loading ? (
            <PartnerGridSkeleton count={9} />
          ) : error ? (
            <ErrorMessage
              message={error}
              onRetry={fetchPartners}
              className="max-w-md mx-auto"
            />
          ) : partners.length > 0 ? (
            <>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {partners.map((partner) => (
                  <PartnerCard key={partner.id} partner={partner} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                  className="mt-12"
                />
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-primary mb-2">Nta bafatanya bikorwa baraboneka ubu</h3>
              <p className="text-primary-dark">
                {selectedCategories.length > 0
                  ? 'Gerageza guhindura inyandiko mu ibyiciro.'
                  : 'Uze kugenzura hanyuma ko hagize ubufatanye bushya.'
                }
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Become a Partner */}
      <section className="section bg-secondary-light">
        <div className="container-custom max-w-3xl mx-auto text-center">
          <h2 className="mb-6">Ba umwe mubaterankunga bacu</h2>
          <p className="text-lg mb-8">
            Twakwishimira kubagira mubaterankunga bacu. Ese amahame yanyu, ahura n'ayacu?
          </p>
          <div className="bg-background p-8 rounded-lg shadow-md text-left border border-secondary-dark">
            <div className="mb-6">
              <h3 className="text-xl mb-4">Inyungu z'ubufatanye</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Kumenyekanisha ibikorwa byanyu muri minisiteri.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Amahirwe yo gufatanya mu ibikorwa n'ibiganiro bitandukanye.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Guhuzwa n'abandi</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>Guteza imbere uyumurimo</span>
                </li>
              </ul>
            </div>
            <Link href="/contact" className="btn btn-primary">Twandikire kubijyanye n'ubufatanye</Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
