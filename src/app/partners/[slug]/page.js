import Link from 'next/link';
import { FaExternalLinkAlt, FaTag } from 'react-icons/fa';
import Header from '../../../components/Header';
import Footer from '../../../components/Footer';
import { api } from '../../../lib/api';
import { notFound } from 'next/navigation';

async function getPartnerData(slug) {
  try {
    const partner = await api.partners.getBySlug(slug);
    return { partner };
  } catch (error) {
    console.error('Error fetching partner:', error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const data = await getPartnerData(params.slug);
  
  if (!data?.partner) {
    return {
      title: 'Partner Not Found - Umugore <PERSON>zashimwa',
      description: 'The requested partner could not be found.',
    };
  }
  
  const { partner } = data;
  return {
    title: `${partner.name} - Our Partners - Umugore <PERSON>`,
    description: partner.description || 'Learn about our valued partner and their mission.',
  };
}

export default async function PartnerPage({ params }) {
  const { slug } = params;
  const data = await getPartnerData(slug);
  
  if (!data?.partner) {
    notFound();
  }
  
  const { partner } = data;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Partner Header */}
      <section className="pt-12 pb-8 bg-primary text-white">
        <div className="container-custom">
          <Link href="/partners" className="inline-block mb-4 hover:underline">
            ← Subira ku bafatanya bikorwa
          </Link>
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="mb-4">{partner.name}</h1>
              {partner.description && (
                <p className="text-lg mb-6">
                  {partner.description}
                </p>
              )}
              
              {/* Categories */}
              {partner.categories && partner.categories.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-6">
                  {partner.categories.map((category) => (
                    <span
                      key={category.slug}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-full"
                    >
                      <FaTag className="text-xs" />
                      {category.name}
                    </span>
                  ))}
                </div>
              )}
              
              {partner.website && (
                <a
                  href={partner.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 bg-accent text-neutral-800 px-6 py-3 rounded-lg font-medium hover:bg-accent-dark transition-colors"
                >
                  Sura urubuga
                  <FaExternalLinkAlt className="text-sm" />
                </a>
              )}
            </div>
            
            {/* Partner Logo/Image */}
            <div className="flex justify-center">
              <div className="w-64 h-64 bg-white bg-opacity-10 rounded-lg flex items-center justify-center">
                {partner.image ? (
                  <img
                    src={partner.image}
                    alt={`${partner.name} logo`}
                    className="max-w-full max-h-full object-contain"
                  />
                ) : (
                  <div className="text-white text-opacity-60 text-center">
                    <div className="text-4xl font-bold mb-2">{partner.name.charAt(0)}</div>
                    <div className="text-sm">Ikimenyetso cy'umufatanyabikorwa</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Partner Details */}
      {/* <section className="py-12 bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto"> */}
            {/* {partner.description && (
              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Ku {partner.name}</h2>
                <div className="prose prose-lg max-w-none">
                  <p>{partner.description}</p>
                </div>
              </div>
            )} */}
            
            {/* Partnership Information */}
            {/* <div className="bg-neutral-50 p-8 rounded-lg">
              <h3 className="text-xl font-semibold mb-4">Amakuru y'ubufatanye</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-neutral-700 mb-2">Icyiciro cy'umuryango</h4>
                  <p className="text-neutral-600">
                    {partner.categories && partner.categories.length > 0
                      ? partner.categories.map(c => c.name).join(', ')
                      : 'Umuryango mufatanyabikorwa'
                    }
                  </p>
                </div>

                {partner.website && (
                  <div>
                    <h4 className="font-medium text-neutral-700 mb-2">Urubuga</h4>
                    <a
                      href={partner.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary-dark flex items-center gap-1"
                    >
                      {partner.website.replace(/^https?:\/\//, '')}
                      <FaExternalLinkAlt className="text-xs" />
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Call to Action */}
      <section className="py-12 bg-secondary-light">
        <div className="container-custom max-w-3xl mx-auto text-center">
          <h2 className="mb-4">Ushaka ubufatanye?</h2>
          <p className="text-lg mb-8">
            Niba umuryango wanyu usangiye intego imwe n'iyacu yo kumenyesha abagore Umuremyi wabo, abo baribo n'icyo bahamagariwe, hashingiwe ku amahame ya Bibiliya, twakwishimira kwakira ubwo bufatanye.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn btn-primary">
              Tuvugishe ku bufatanye
            </Link>
            <Link href="/partners" className="btn btn-outline">
              Reba abafatanyabikorwa bose
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
