import Link from 'next/link';
import { FaCalendarAlt } from 'react-icons/fa';
import Header from '../../../components/Header';
import Footer from '../../../components/Footer';
import ShareButtons from '../../../components/ShareButtons';
import { api } from '../../../lib/api';
import { SITE_CONFIG } from '../../../lib/constants';
import { notFound } from 'next/navigation';
import { sanitizeHtml, stripHtml } from '../../../lib/htmlUtils';

async function getTestimonyData(id) {
  try {
    const [testimony, relatedTestimonies] = await Promise.all([
      api.testimonies.getById(id),
      api.testimonies.getAll({ pageSize: 3 })
    ]);
    
    return {
      testimony,
      relatedTestimonies: relatedTestimonies.results?.filter(t => t.id !== id).slice(0, 3) || []
    };
  } catch (error) {
    console.error('Error fetching testimony:', error);
    return null;
  }
}

export async function generateMetadata({ params }) {
  const data = await getTestimonyData(params.id);

  if (!data?.testimony) {
    return {
      title: 'Testimony Not Found - Umugore Uzashimwa',
      description: 'The requested testimony could not be found.',
    };
  }

  const { testimony } = data;
  const description = testimony.content
    ? stripHtml(testimony.content).substring(0, 160) + '...'
    : 'Read this inspiring testimony from our community.';

  const testimonyUrl = `${SITE_CONFIG.url}/testimonies/${params.id}`;
  const imageUrl = testimony.image || SITE_CONFIG.defaultImage;

  return {
    title: `${testimony.name}'s Testimony - Umugore Uzashimwa`,
    description,
    openGraph: {
      title: `${testimony.name}'s Testimony`,
      description,
      url: testimonyUrl,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: `${testimony.name}'s Testimony`,
        },
      ],
      locale: 'en_US',
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${testimony.name}'s Testimony`,
      description,
      images: [imageUrl],
    },
    alternates: {
      canonical: testimonyUrl,
    },
  };
}

export default async function TestimonyPage({ params }) {
  const { id } = params;
  const data = await getTestimonyData(id);

  if (!data?.testimony) {
    notFound();
  }

  const { testimony, relatedTestimonies } = data;



  // Format date
  const displayDate = testimony.created_at ? new Date(testimony.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : '';

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Testimony Header */}
      <section className="pt-12 pb-8 bg-primary text-white">
        <div className="container-custom">
          <Link href="/testimonies" className="inline-block mb-4 hover:underline">
            ← Subira ku buhamya
          </Link>
          <h1 className="mb-4">Ubuhamya bwa {testimony.name}</h1>
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-white bg-opacity-20 mr-3 relative overflow-hidden">
                {testimony.image ? (
                  <img 
                    src={testimony.image} 
                    alt={testimony.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center text-white font-semibold">
                    {testimony.name.charAt(0)}
                  </div>
                )}
              </div>
              <div>
                <div className="font-medium">{testimony.name}</div>
                {testimony.email && (
                  <div className="text-sm text-white text-opacity-80">{testimony.email}</div>
                )}
              </div>
            </div>
            {displayDate && (
              <div className="flex items-center">
                <FaCalendarAlt className="mr-2" />
                <span>{displayDate}</span>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Testimony Content */}
      <section className="py-12 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-8">
              {testimony.image && (
                <div className="w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-8 overflow-hidden">
                  <img 
                    src={testimony.image} 
                    alt={testimony.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              <div className="text-center mb-8">
                <div className="text-accent text-6xl font-serif mb-4">"</div>
                <div className="prose prose-xl max-w-none text-center">
                  <div
                    className="text-lg leading-relaxed text-justify"
                    dangerouslySetInnerHTML={{ __html: sanitizeHtml(testimony.content) }}
                  />
                </div>
                <div className="text-accent text-6xl font-serif mt-4 rotate-180">"</div>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold">{testimony.name}</h3>
                <p className="text-neutral-600">Umunyamuryango</p>
              </div>
              
              {/* Share */}
              <div className="mt-12 pt-8 border-t text-center">
                <ShareButtons
                  item={testimony}
                  type="testimony"
                  title="Sangiza abandi ubu buhamya"
                  className="flex flex-col items-center"
                />
              </div>
            </div>
            
            {/* Sidebar */}
            <div className="lg:col-span-4">
              {relatedTestimonies.length > 0 && (
                <div className="bg-neutral-50 p-6 rounded-lg mb-8">
                  <h3 className="text-xl mb-4">Ubuhamya bwinshi</h3>
                  <div className="space-y-6">
                    {relatedTestimonies.map((relatedTestimony) => (
                      <div key={relatedTestimony.id} className="border-b border-neutral-200 pb-4 last:border-b-0">
                        <Link href={`/testimonies/${relatedTestimony.id}`} className="block">
                          <div className="flex gap-3">
                            <div className="w-12 h-12 rounded-full bg-neutral-300 flex-shrink-0 overflow-hidden">
                              {relatedTestimony.image ? (
                                <img 
                                  src={relatedTestimony.image} 
                                  alt={relatedTestimony.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center text-neutral-500 font-semibold">
                                  {relatedTestimony.name.charAt(0)}
                                </div>
                              )}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium hover:text-primary">{relatedTestimony.name}</h4>
                              <p className="text-sm text-neutral-600 line-clamp-2">
                                {stripHtml(relatedTestimony.content).substring(0, 80)}...
                              </p>
                            </div>
                          </div>
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="bg-primary text-white p-6 rounded-lg">
                <h3 className="text-xl mb-4">Sangiza inkuru yawe</h3>
                <p className="mb-4">Ese nawe haricyo wasangiza abandi? Twakwishimira kumva ubuhamya bwawe.</p>
                <Link href="/testimonies#share" className="btn bg-accent text-neutral-800 hover:bg-accent-dark w-full text-center block">
                  Sangiza ubuhamya bwawe
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
