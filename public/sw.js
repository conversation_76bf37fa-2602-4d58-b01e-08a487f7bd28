// Service Worker for caching API responses and static assets
// Update version number to force cache refresh - FIXED TESTIMONY SHARING HTML TAGS
const CACHE_VERSION = Date.now(); // Force cache refresh for testimony sharing fix
const CACHE_NAME = `umugore-uzashimwa-v${CACHE_VERSION}`;
const API_CACHE_NAME = `umugore-uzashimwa-api-v${CACHE_VERSION}`;

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/articles',
  '/testimonies',
  '/partners',
  '/about',
  '/contact',
  '/offline.html',
  '/manifest.json'
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/articles/',
  '/api/testimonies/',
  '/api/partners/',
  '/api/base/categories/',
  '/api/accounts/authors/',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
  );
  // Skip waiting to activate immediately when update is available
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  // Claim all clients immediately
  self.clients.claim();
});

// Handle messages from the client
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests - FORCE FRESH FETCH FOR NEW API URL
  if (url.pathname.startsWith('/api/') || url.hostname === 'api.umugoreuzashimwa.org') {
    event.respondWith(
      // Always fetch fresh data first to ensure we get responses from new API URL
      fetch(request).then((response) => {
        if (response.ok) {
          // Cache successful responses for future use
          caches.open(API_CACHE_NAME).then((cache) => {
            cache.put(request, response.clone());
          });
        }
        return response;
      }).catch(() => {
        // Only fall back to cache if network fails
        return caches.open(API_CACHE_NAME).then((cache) => {
          return cache.match(request).then((cachedResponse) => {
            if (cachedResponse) {
              return cachedResponse;
            }
            // Return a basic error response if both network and cache fail
            return new Response(
              JSON.stringify({ error: 'Network error, please try again later' }),
              {
                status: 503,
                statusText: 'Service Unavailable',
                headers: { 'Content-Type': 'application/json' }
              }
            );
          });
        });
      })
    );
    return;
  }

  // Handle static assets
  event.respondWith(
    caches.match(request).then((response) => {
      // Return cached version or fetch from network
      return response || fetch(request).catch(() => {
        // If both cache and network fail, show offline page for navigation requests
        if (request.mode === 'navigate') {
          return caches.match('/offline.html');
        }
        // For other requests, return a basic response
        return new Response('Offline', { status: 503, statusText: 'Service Unavailable' });
      });
    })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle any background sync tasks here
      console.log('Background sync triggered')
    );
  }
});

// Push notification handling (for future use)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icons/no-bg-logo.png',
      badge: '/icons/no-bg-logo.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey || 1
      }
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  event.waitUntil(
    clients.openWindow('/')
  );
});
