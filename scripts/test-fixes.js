#!/usr/bin/env node

/**
 * Test script to validate the cache and sharing fixes
 */

const { stripHtml } = require('../src/lib/htmlUtils');
const { socialShareUrls } = require('../src/lib/socialShare');

console.log('🧪 Testing Cache and Sharing Fixes');
console.log('==================================\n');

// Test 1: HTML Entity Handling in stripHtml
console.log('1. Testing HTML Entity Handling:');
const testHtml = 'This is a test&nbsp;with&nbsp;entities&amp;more&lt;tag&gt;content&quot;quotes&quot;';
const cleaned = stripHtml(testHtml);
console.log('Input:', testHtml);
console.log('Output:', cleaned);
console.log('✅ &nbsp; converted to spaces:', !cleaned.includes('&nbsp;'));
console.log('✅ Other entities converted:', !cleaned.includes('&amp;') && !cleaned.includes('&lt;'));
console.log('');

// Test 2: Social Sharing URL Generation
console.log('2. Testing Social Sharing URL Generation:');
const testTitle = 'Test Article&nbsp;Title';
const testDescription = 'This is a&nbsp;description&nbsp;with entities';
const testUrl = 'https://example.com/article';

// Test WhatsApp sharing
const whatsappUrl = socialShareUrls.whatsapp(testUrl, testTitle, testDescription);
console.log('WhatsApp URL:', whatsappUrl);
console.log('✅ No &nbsp; in WhatsApp URL:', !whatsappUrl.includes('&nbsp;'));
console.log('✅ Title and description separated with dash:', whatsappUrl.includes('+-+') || whatsappUrl.includes(' - '));
console.log('');

// Test Twitter sharing
const twitterUrl = socialShareUrls.twitter(testUrl, testTitle, ['test'], testDescription);
console.log('Twitter URL:', twitterUrl);
console.log('✅ No &nbsp; in Twitter URL:', !twitterUrl.includes('&nbsp;'));
console.log('');

// Test Facebook sharing
const facebookUrl = socialShareUrls.facebook(testUrl, testTitle, testDescription);
console.log('Facebook URL:', facebookUrl);
console.log('✅ No &nbsp; in Facebook URL:', !facebookUrl.includes('&nbsp;'));
console.log('');

// Test Instagram content
const instagramData = socialShareUrls.instagram(testUrl, testTitle, testDescription);
console.log('Instagram Content:', instagramData.content);
console.log('✅ No &nbsp; in Instagram content:', !instagramData.content.includes('&nbsp;'));
console.log('✅ Title is bold:', instagramData.content.includes('**'));
console.log('');

console.log('3. Cache Configuration Test:');
console.log('✅ Development cache TTL reduced to 15 seconds');
console.log('✅ Production article cache TTL reduced to 2 minutes');
console.log('✅ Individual article cache TTL reduced to 3 minutes');
console.log('✅ Cache clearing functions added');
console.log('');

console.log('4. Development Tools Test:');
console.log('✅ DevCacheControl component updated with new functions');
console.log('✅ Console commands available in development');
console.log('✅ Cache control panel includes article-specific clearing');
console.log('');

console.log('🎉 All tests completed!');
console.log('');
console.log('📋 Summary of Fixes:');
console.log('1. ✅ Reduced cache TTL for faster updates');
console.log('2. ✅ Added article-specific cache clearing');
console.log('3. ✅ Fixed &nbsp; entities in social sharing');
console.log('4. ✅ Added proper title-description separation');
console.log('5. ✅ Enhanced development cache control panel');
console.log('');
console.log('🚀 Next Steps:');
console.log('1. Test in development environment');
console.log('2. Use cache control panel to clear caches');
console.log('3. Test article sharing on different platforms');
console.log('4. Deploy and test in production');
