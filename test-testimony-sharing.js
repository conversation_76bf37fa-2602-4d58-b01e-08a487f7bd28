#!/usr/bin/env node

/**
 * Test script to verify testimony sharing HTML stripping works correctly
 */

// Import the generateShareData function
const { generateShareData } = require('./src/lib/socialShare.js');

console.log('🧪 Testing Testimony Sharing HTML Stripping');
console.log('===========================================\n');

// Test testimony with HTML content (similar to the issue reported)
const testTestimony = {
  id: '52ffa3a3-8be8-4274-8247-b4f5c460b486',
  name: '<PERSON><PERSON>',
  content: '<h1><strong>Agahinda k\'Amahitamo</strong></h1><h2><br><strong>Ubuhamya bwa Tessie</strong></h2><p>&nbsp;</p><p>"Umugore wayobowe n\'amarangamutima, aho kuyoborwa n\'ubwoba, ni we uzashimwa. Ibi byose byatangiye mu gihe nabonaga ko ntagishobora kwihangana n\'ubuzima bwanjye. Nabaye nkumuntu utazi icyo ashaka, utazi aho ajya..."</p>',
  image: null,
  email: '<EMAIL>'
};

// Test the generateShareData function
console.log('📝 Original testimony content:');
console.log(testTestimony.content);
console.log('\n');

const shareData = generateShareData(testTestimony, 'testimony', 'https://umugoreuzashimwa.org');

console.log('✅ Generated share data:');
console.log('Title:', shareData.title);
console.log('Description:', shareData.description);
console.log('URL:', shareData.url);
console.log('\n');

// Verify HTML tags are stripped
const hasHtmlTags = /<[^>]*>/g.test(shareData.description);

if (hasHtmlTags) {
  console.log('❌ FAILED: HTML tags still present in description');
  console.log('Description contains HTML:', shareData.description);
  process.exit(1);
} else {
  console.log('✅ SUCCESS: HTML tags properly stripped from description');
  console.log('Clean description:', shareData.description);
}

// Test with article for comparison
console.log('\n📰 Testing article sharing (should also work):');
const testArticle = {
  slug: 'test-article',
  title: 'Test Article',
  content: '<p>This is a <strong>test article</strong> with <em>HTML tags</em>.</p>',
  image: null
};

const articleShareData = generateShareData(testArticle, 'article', 'https://umugoreuzashimwa.org');
const articleHasHtmlTags = /<[^>]*>/g.test(articleShareData.description);

if (articleHasHtmlTags) {
  console.log('❌ FAILED: Article HTML tags still present');
  process.exit(1);
} else {
  console.log('✅ SUCCESS: Article HTML tags also properly stripped');
  console.log('Article description:', articleShareData.description);
}

console.log('\n🎉 All tests passed! Testimony sharing will now work without HTML tags.');
console.log('\nWhat users will see when sharing:');
console.log('Instead of: "<h1><strong>Agahinda k\'Amahitamo</strong></h1><h2><br><strong>Ubuhamya bwa Tessie</strong></h2><p>&nbsp;</p><p>"Umugore wayobowe..."');
console.log('They will see: "Agahinda k\'Amahitamo Ubuhamya bwa Tessie   "Umugore wayobowe n\'amarangamutima, aho kuyoborwa n\'ubwoba, ni we uzashimwa. Ibi byose byatangiye mu gihe nabonaga ko ntagishobora kwihangana n\'ubuzima bwanjye..."');
